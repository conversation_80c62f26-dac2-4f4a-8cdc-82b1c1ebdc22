import pandas as pd
import numpy as np 
from scipy import spatial
import operator


movieDict = {}

def NormalizePoints():
    role_col = ['user_id', 'movie_id','rating']

    ratings = pd.read_csv('u.data',sep = '\t', names = role_col,usecols=range(3))

    print(ratings.head())

    movieProperties = ratings.groupby('movie_id').agg({'rating' : [np.size , np.mean]})
    print(movieProperties.head())

    movieNumRating = pd.DataFrame(movieProperties['rating']['size'])
    print(movieNumRating.head())

    movieNormalizedNumRating = movieNumRating.apply(lambda x: (x - np.min(x)) / (np.max(x) - np.min(x)))
    print(movieNormalizedNumRating.head())
    

    

    with open ('u.item') as f :
         temp = ''
         for line in f :
              fields = line.rstrip('\n').split('|')
              movieID = int(fields[0])  # Convert to integer
              name = fields[1]
              genres = fields[5:25]
              genres = list(map(int,genres))  # Convert map to list for Python 3 compatibility

              movieDict[movieID] = (name, genres,movieNormalizedNumRating.loc[movieID].get('size'), movieProperties.loc[movieID].rating.get('mean'))
                                      
    print(movieDict[1])

    def ComputeDistance(a,b):
        genresA = a[1]
        genresB = b[1]

        genresDistance = spatial.distance.cosine(genresA, genresB)

        popularityA = a[2]
        popularityB = b[2]

        popularityDistance = abs(popularityA - popularityB)
        return genresDistance + popularityDistance
    
    print(ComputeDistance(movieDict[2],movieDict[4]))

    def getNeighbors(movieID, K):
        distance =[]
        for movie in movieDict: 
              if (movie != movieID):
                  dist = ComputeDistance(movieDict[movieID], movieDict[movie])
                  distance.append((movie, dist))

        distance.sort(key = operator.itemgetter(1))
        neighbors = []
        for x in range(K):
             neighbors.append(distance[x][0])
        return neighbors
    
    

    k = 10 
    averageRating = 0 
    neighbors = getNeighbors(1, k)
    for neighbor in neighbors :
         averageRating += movieDict[neighbor][3]
         print(movieDict[neighbor][0] + " : " + str(movieDict[neighbor][3]))

    averageRating /= k
          
    # Compute distance between two points in a 2D space. The function returns the Euclidean Distance (ED). ED is computed as follows: ED = sqrt((x2 - x1) ^ 2 + (y2 - y1) ^ 2)omputeDistance()

                                    
    
              
    

    
NormalizePoints()
