import numpy as np
from scipy.optimize import curve_fit
import matplotlib.pyplot as plt

# 1. Generate synthetic data (or load your own data)
# Define the true sine wave parameters
amplitude_true = 2.0
frequency_true = 0.5  # In Hz
phase_true = np.pi / 4
offset_true = 1.0

# Generate x-values
x_data = np.linspace(0, 10, 100)

# Generate y-values with added noise
y_true = amplitude_true * np.sin(2 * np.pi * frequency_true * x_data + phase_true) + offset_true
np.random.seed(42) # for reproducibility
y_data = y_true + np.random.normal(0, 0.3, size=len(x_data))

# 2. Define the sinusoidal function to fit
def sine_function(x, amplitude, frequency, phase, offset):
    return amplitude * np.sin(2 * np.pi * frequency * x + phase) + offset

def sine_function2(x, amplitude1,amplitude2, frequency1 , frequency2, phase1,phase2, offset):
    return amplitude1 * np.sin(2 * np.pi * frequency1 * x + phase1) + amplitude2* np.sin(2 * np.pi * frequency2 * x + phase2) + offset

# 3. Perform the curve fitting
# Provide initial guesses for the parameters (can help with convergence)
initial_guesses = [1.5,0.2, 0.6,0.1, 0.0,0.1, 0.5]

# Use curve_fit to find the optimal parameters
params, covariance = curve_fit(sine_function2, x_data, y_data, p0=initial_guesses)

# Extract the fitted parameters
# amplitude_fit, frequency_fit, phase_fit, offset_fit = params

amplitude_fit1 , amplitude_fit2 , frequency_fit1 , frequency_fit2 , phase_fit1,  phase_fit2, offset_fit = params

# 4. Generate the fitted curve
y_fit = sine_function2(amplitude_fit1 , amplitude_fit2 , frequency_fit1 , frequency_fit2 , phase_fit1,  phase_fit2, offset_fit)

# 5. Visualize the results
plt.figure(figsize=(10, 6))
plt.scatter(x_data, y_data, label='Noisy Data', s=10)
plt.plot(x_data, y_true, label='True Sine Wave', color='green', linestyle='--')
plt.plot(x_data, y_fit, label='Fitted Sine Wave', color='red')
plt.title('Sine Regression in Python')
plt.xlabel('X')
plt.ylabel('Y')
plt.legend()
plt.grid(True)
plt.show()

print(f"True Parameters: Amplitude={amplitude_true:.2f}, Frequency={frequency_true:.2f}, Phase={phase_true:.2f}, Offset={offset_true:.2f}")
print(f"Fitted Parameters: Amplitude={amplitude_fit:.2f}, Frequency={frequency_fit:.2f}, Phase={phase_fit:.2f}, Offset={offset_fit:.2f}")